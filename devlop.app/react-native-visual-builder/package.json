{"name": "react-native-visual-builder", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "assert": "^2.1.0", "autoprefixer": "^10.4.21", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "https-browserify": "^1.0.0", "jszip": "^3.10.1", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss": "^8.5.4", "process": "^0.11.10", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "reactflow": "^11.11.4", "snack-sdk": "^6.5.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tailwindcss": "^3.4.17", "url": "^0.11.4", "util": "^0.12.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-app-rewired": "^2.2.1"}}